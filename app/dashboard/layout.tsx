import type React from "react"
import { cookies } from "next/headers"
import { AppSidebar } from "@/components/app-sidebar"
import { SidebarProvider } from "@/components/ui/sidebar"
import { getLoggedInUser } from "@/lib/data"
import { AppHeader } from "@/components/app-header"

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const cookieStore = cookies()
  const defaultOpen = cookieStore.get("sidebar:state")?.value !== "false"
  // Set to 'hr-admin' to view all HR panels
  const user = await getLoggedInUser("hr-admin")

  return (
    <SidebarProvider defaultOpen={defaultOpen}>
      <AppSidebar user={user} />
      <main className="flex-1 bg-muted/40">
        <AppHeader />
        {children}
      </main>
    </SidebarProvider>
  )
}
