import { getManagerAppraisals } from "@/lib/data"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { StatusBadge } from "@/components/status-badge"
import { ArrowRight } from "lucide-react"

export default async function ManagerDashboardPage() {
  const appraisals = await getManagerAppraisals()

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">My Team</h1>
        <p className="text-muted-foreground">July 2025 appraisal period.</p>
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {appraisals.map((appraisal) => (
          <Card key={appraisal.employeeId}>
            <CardHeader>
              <CardTitle>{appraisal.fullName}</CardTitle>
              <CardDescription>{appraisal.departmentName}</CardDescription>
            </CardHeader>
            <CardContent>
              <StatusBadge status={appraisal.status} />
              {appraisal.status === "submitted" && appraisal.submittedAt && (
                <p className="mt-2 text-xs text-muted-foreground">
                  Submitted on {new Date(appraisal.submittedAt).toLocaleDateString()}
                </p>
              )}
            </CardContent>
            <CardFooter>
              <Button size="sm" asChild>
                <a href={`/dashboard/appraisal/${appraisal.employeeId}`}>
                  {appraisal.status === "not-started" ? "Start Appraisal" : "View / Edit"}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </a>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
