import { getDepartments } from "@/lib/data"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DepartmentsTable } from "@/components/departments-table"

export default async function DepartmentsPage() {
  const departments = await getDepartments()

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Department Management</h1>
        <p className="text-muted-foreground">Add, edit, and manage all company departments.</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>All Departments</CardTitle>
          <CardDescription>A list of all departments in the system.</CardDescription>
        </CardHeader>
        <CardContent>
          <DepartmentsTable data={departments} />
        </CardContent>
      </Card>
    </div>
  )
}
