import { getAccountingData } from "@/lib/data"
import { AccountingTable } from "@/components/accounting-table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default async function ApprovalsPage() {
  const data = await getAccountingData()

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Payroll Approvals</h1>
        <p className="text-muted-foreground">Status of all appraisals for the July 2025 period.</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Appraisal Status</CardTitle>
          <CardDescription>
            Review the status of all employee appraisals. Export submitted appraisals for payroll processing.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AccountingTable data={data} />
        </CardContent>
      </Card>
    </div>
  )
}
