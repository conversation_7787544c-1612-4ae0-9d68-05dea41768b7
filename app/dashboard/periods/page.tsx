import { getPeriods } from "@/lib/data"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PeriodsTable } from "@/components/periods-table"

export default async function PeriodsPage() {
  const periods = await getPeriods()

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Appraisal Period Management</h1>
        <p className="text-muted-foreground">Open new periods and close old ones.</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>All Appraisal Periods</CardTitle>
          <CardDescription>A list of all past and current appraisal periods.</CardDescription>
        </CardHeader>
        <CardContent>
          <PeriodsTable data={periods} />
        </CardContent>
      </Card>
    </div>
  )
}
