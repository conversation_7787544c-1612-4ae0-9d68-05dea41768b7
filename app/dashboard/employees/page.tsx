import { getEmployees, getDepartments } from "@/lib/data"
import { EmployeesTable } from "@/components/employees-table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default async function EmployeesPage() {
  const employees = await getEmployees()
  const departments = await getDepartments()

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Employee Management</h1>
        <p className="text-muted-foreground">Add, edit, and manage all company employees.</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>All Employees</CardTitle>
          <CardDescription>A list of all employees in the system.</CardDescription>
        </CardHeader>
        <CardContent>
          <EmployeesTable data={employees} departments={departments} />
        </CardContent>
      </Card>
    </div>
  )
}
