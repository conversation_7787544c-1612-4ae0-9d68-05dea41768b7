"use client"

import * as React from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { Employee, Department, Manager } from "@/lib/types"
import { saveEmployeeAction } from "@/lib/actions"
import { useTransition } from "react"

interface EmployeeFormDialogProps {
  isOpen: boolean
  onClose: () => void
  employee: Employee | null
  departments: Department[]
  managers: Manager[]
}

export function EmployeeFormDialog({ isOpen, onClose, employee, departments, managers }: EmployeeFormDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [formData, setFormData] = React.useState<Partial<Employee>>({})

  React.useEffect(() => {
    if (employee) {
      setFormData(employee)
    } else {
      setFormData({
        fullName: "",
        departmentId: "",
        managerId: null,
        compensation: "monthly",
        rate: 0,
        active: true,
      })
    }
  }, [employee, isOpen])

  const handleSave = () => {
    startTransition(async () => {
      await saveEmployeeAction(formData)
      onClose()
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{employee ? "Edit Employee" : "Add New Employee"}</DialogTitle>
          <DialogDescription>
            {employee ? "Make changes to the employee profile." : "Add a new employee to the system."}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="fullName" className="text-right">
              Full Name
            </Label>
            <Input
              id="fullName"
              value={formData.fullName || ""}
              onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="department" className="text-right">
              Department
            </Label>
            <Select
              value={formData.departmentId}
              onValueChange={(value) => setFormData({ ...formData, departmentId: value })}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select a department" />
              </SelectTrigger>
              <SelectContent>
                {departments.map((dept) => (
                  <SelectItem key={dept.id} value={dept.id}>
                    {dept.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="manager" className="text-right">
              Manager
            </Label>
            <Select
              value={formData.managerId || ""}
              onValueChange={(value) => setFormData({ ...formData, managerId: value })}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Assign a manager" />
              </SelectTrigger>
              <SelectContent>
                {managers.map((manager) => (
                  <SelectItem key={manager.id} value={manager.id}>
                    {manager.fullName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">Compensation</Label>
            <RadioGroup
              value={formData.compensation}
              onValueChange={(value) => setFormData({ ...formData, compensation: value as Employee["compensation"] })}
              className="col-span-3 flex space-x-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="monthly" id="monthly" />
                <Label htmlFor="monthly">Monthly</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="hourly" id="hourly" />
                <Label htmlFor="hourly">Hourly</Label>
              </div>
            </RadioGroup>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="rate" className="text-right">
              Rate (USD)
            </Label>
            <Input
              id="rate"
              type="number"
              value={formData.rate || ""}
              onChange={(e) => setFormData({ ...formData, rate: Number.parseFloat(e.target.value) || 0 })}
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isPending}>
            {isPending ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
