"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON>, DialogContent, <PERSON>alogFooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form"
import { AlertTriangle, Loader, Calendar } from "lucide-react"
import type { AppraisalPeriod } from "@/lib/types"
import { appraisalPeriodFormSchema, type AppraisalPeriod as AppraisalPeriodSchema } from "@/lib/schemas"
import { savePeriodAction } from "@/lib/actions"
import { useTransition } from "react"

interface PeriodFormDialogProps {
  isOpen: boolean
  onClose: () => void
  period: AppraisalPeriod | null
}

type PeriodFormData = Omit<AppraisalPeriodSchema, 'id'>

export function PeriodFormDialog({ isOpen, onClose, period }: PeriodFormDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [submitError, setSubmitError] = React.useState<string | null>(null)

  const form = useForm<PeriodFormData>({
    resolver: zodResolver(appraisalPeriodFormSchema),
    defaultValues: {
      periodStart: "",
      periodEnd: "",
      closed: false,
    },
  })

  // Reset form when dialog opens/closes or period changes
  React.useEffect(() => {
    if (isOpen) {
      setSubmitError(null)
      if (period) {
        form.reset({
          periodStart: period.periodStart,
          periodEnd: period.periodEnd,
          closed: period.closed,
        })
      } else {
        // Set default values for new period (current month)
        const today = new Date()
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split("T")[0]
        const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString().split("T")[0]
        form.reset({
          periodStart: startOfMonth,
          periodEnd: endOfMonth,
          closed: false,
        })
      }
    }
  }, [period, isOpen, form])

  const onSubmit = async (data: PeriodFormData) => {
    setSubmitError(null)

    startTransition(async () => {
      try {
        // Create FormData for server action
        const formData = new FormData()
        if (period?.id) {
          formData.append('id', period.id)
        }
        formData.append('periodStart', data.periodStart)
        formData.append('periodEnd', data.periodEnd)
        formData.append('closed', data.closed.toString())

        const result = await savePeriodAction(formData)

        if (result.success) {
          onClose()
        } else {
          setSubmitError(result.error || 'Failed to save appraisal period')
        }
      } catch (error) {
        console.error('Form submission error:', error)
        setSubmitError('An unexpected error occurred. Please try again.')
      }
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {period ? "Edit Appraisal Period" : "Create New Appraisal Period"}
            </div>
          </DialogTitle>
        </DialogHeader>

        {submitError && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="periodStart"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Start Date</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormDescription>
                    The first day of the appraisal period
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="periodEnd"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>End Date</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormDescription>
                    The last day of the appraisal period
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="closed"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isPending}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Mark period as closed
                    </FormLabel>
                    <FormDescription>
                      Closed periods cannot be modified and no new appraisals can be submitted
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isPending || !form.formState.isValid}
              >
                {isPending ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  period ? "Update Period" : "Create Period"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
