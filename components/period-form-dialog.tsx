"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import type { AppraisalPeriod } from "@/lib/types"
import { savePeriodAction } from "@/lib/actions"
import { useTransition } from "react"

interface PeriodFormDialogProps {
  isOpen: boolean
  onClose: () => void
  period: AppraisalPeriod | null
}

export function PeriodFormDialog({ isOpen, onClose, period }: PeriodFormDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [formData, setFormData] = React.useState<Partial<AppraisalPeriod>>({})

  React.useEffect(() => {
    if (period) {
      setFormData(period)
    } else {
      const today = new Date()
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split("T")[0]
      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString().split("T")[0]
      setFormData({
        periodStart: startOfMonth,
        periodEnd: endOfMonth,
        closed: false,
      })
    }
  }, [period, isOpen])

  const handleSave = () => {
    startTransition(async () => {
      await savePeriodAction(formData)
      onClose()
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{period ? "Edit Period" : "Open New Period"}</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="periodStart" className="text-right">
              Start Date
            </Label>
            <Input
              id="periodStart"
              type="date"
              value={formData.periodStart || ""}
              onChange={(e) => setFormData({ ...formData, periodStart: e.target.value })}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="periodEnd" className="text-right">
              End Date
            </Label>
            <Input
              id="periodEnd"
              type="date"
              value={formData.periodEnd || ""}
              onChange={(e) => setFormData({ ...formData, periodEnd: e.target.value })}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="closed" className="text-right">
              Status
            </Label>
            <div className="col-span-3 flex items-center space-x-2">
              <Checkbox
                id="closed"
                checked={formData.closed}
                onCheckedChange={(checked) => setFormData({ ...formData, closed: !!checked })}
              />
              <Label htmlFor="closed">Mark period as closed</Label>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isPending}>
            {isPending ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
