"use client"

import { useState, useTransition } from "react"
import type { AppraisalDetails, EmployeeDetails } from "@/lib/types"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Send, Loader, CheckCircle, AlertCircle } from "lucide-react"
import { useAutosave } from "@/hooks/use-autosave"
import { saveAppraisalDraftAction, submitAppraisalAction } from "@/lib/actions"

const appraisalQuestions = {
  q1: "Overall performance this month:",
  q2: "Completed all assigned tasks?",
  q3: "Primary project/focus area:",
  q4: "Achievements and areas for improvement:",
  q5: "Next month's goals and focus:",
}

type AppraisalFormProps = {
  employee: EmployeeDetails
  appraisal: AppraisalDetails | null
}

function AutosaveStatusIndicator({ status }: { status: ReturnType<typeof useAutosave> }) {
  switch (status) {
    case "saving":
      return (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Loader className="h-4 w-4 animate-spin" />
          <span>Saving...</span>
        </div>
      )
    case "success":
      return (
        <div className="flex items-center gap-2 text-sm text-green-600">
          <CheckCircle className="h-4 w-4" />
          <span>Draft saved</span>
        </div>
      )
    case "error":
      return (
        <div className="flex items-center gap-2 text-sm text-red-600">
          <AlertCircle className="h-4 w-4" />
          <span>Save failed</span>
        </div>
      )
    default:
      return <div className="h-6" /> // Placeholder for idle state
  }
}

export function AppraisalForm({ employee, appraisal }: AppraisalFormProps) {
  const [isPending, startTransition] = useTransition()
  const [formData, setFormData] = useState<Partial<AppraisalDetails>>({
    q1: appraisal?.q1 ?? null,
    q2: appraisal?.q2 ?? false,
    q3: appraisal?.q3 ?? "",
    q4: appraisal?.q4 ?? "",
    q5: appraisal?.q5 ?? "",
  })

  const autosaveStatus = useAutosave({
    data: formData,
    onSave: saveAppraisalDraftAction,
  })

  const handleInputChange = (
    field: keyof Omit<AppraisalDetails, "id" | "periodId" | "employeeId" | "managerId" | "status">,
    value: any,
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = () => {
    startTransition(async () => {
      await submitAppraisalAction({ ...formData, employeeId: employee.id })
      alert("Appraisal submitted!")
    })
  }

  const isSubmitted = appraisal?.status === "submitted"

  return (
    <div className="grid gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Monthly Review</CardTitle>
          <CardDescription>
            Fill out the form below for {employee.fullName}. Your progress is saved as a draft automatically.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          {/* Questions... (content is unchanged) */}
          <div className="space-y-2">
            <Label>{appraisalQuestions.q1}</Label>
            <RadioGroup
              value={formData.q1 ?? ""}
              onValueChange={(value) => handleInputChange("q1", value as AppraisalDetails["q1"])}
              className="flex flex-col space-y-2 pt-2 sm:flex-row sm:space-y-0 sm:space-x-4"
              disabled={isSubmitted}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="below-expectations" id="q1-below" />
                <Label htmlFor="q1-below">Below Expectations</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="meets-expectations" id="q1-meets" />
                <Label htmlFor="q1-meets">Meets Expectations</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="exceeds-expectations" id="q1-exceeds" />
                <Label htmlFor="q1-exceeds">Exceeds Expectations</Label>
              </div>
            </RadioGroup>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="q2"
              checked={formData.q2}
              onCheckedChange={(checked) => handleInputChange("q2", !!checked)}
              disabled={isSubmitted}
            />
            <Label
              htmlFor="q2"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {appraisalQuestions.q2}
            </Label>
          </div>
          <div className="space-y-2">
            <Label htmlFor="q3">{appraisalQuestions.q3}</Label>
            <Input
              id="q3"
              value={formData.q3}
              onChange={(e) => handleInputChange("q3", e.target.value)}
              placeholder="e.g., Project Phoenix, Q3 Marketing Campaign"
              disabled={isSubmitted}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="q4">{appraisalQuestions.q4}</Label>
            <Textarea
              id="q4"
              value={formData.q4}
              onChange={(e) => handleInputChange("q4", e.target.value)}
              placeholder="Summarize key accomplishments and any challenges faced..."
              rows={5}
              disabled={isSubmitted}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="q5">{appraisalQuestions.q5}</Label>
            <Input
              id="q5"
              value={formData.q5}
              onChange={(e) => handleInputChange("q5", e.target.value)}
              placeholder="e.g., Lead design for new feature, improve API documentation"
              disabled={isSubmitted}
            />
          </div>
        </CardContent>
        <CardFooter className="flex items-center justify-end gap-4">
          {isSubmitted ? (
            <p className="text-sm text-muted-foreground">This appraisal was submitted and is read-only.</p>
          ) : (
            <>
              <AutosaveStatusIndicator status={autosaveStatus} />
              <Button onClick={handleSubmit} disabled={isPending}>
                {isPending ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" /> Submitting...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" /> Submit Appraisal
                  </>
                )}
              </Button>
            </>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}
