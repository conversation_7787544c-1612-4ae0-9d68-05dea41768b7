"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTit<PERSON> } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import type { Department } from "@/lib/types"
import { saveDepartmentAction } from "@/lib/actions"
import { useTransition } from "react"

interface DepartmentFormDialogProps {
  isOpen: boolean
  onClose: () => void
  department: Department | null
}

export function DepartmentFormDialog({ isOpen, onClose, department }: DepartmentFormDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [name, setName] = React.useState("")

  React.useEffect(() => {
    setName(department?.name || "")
  }, [department, isOpen])

  const handleSave = () => {
    startTransition(async () => {
      await saveDepartmentAction({ id: department?.id, name })
      onClose()
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{department ? "Edit Department" : "Add New Department"}</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name
            </Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="col-span-3"
              placeholder="e.g. Human Resources"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isPending}>
            {isPending ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
