"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { StatusBadge } from "@/components/status-badge"
import type { AccountingViewData } from "@/lib/types"
import { Download } from "lucide-react"

export function AccountingTable({ data }: { data: AccountingViewData[] }) {
  const handleExport = () => {
    const submittedAppraisals = data.filter((item) => item.status === "submitted")
    if (submittedAppraisals.length === 0) {
      alert("No submitted appraisals to export.")
      return
    }

    const headers = ["Employee", "Hours", "Pay Rate", "Manager", "Timestamp"]
    const csvRows = [
      headers.join(","),
      ...submittedAppraisals.map((row) =>
        [
          `"${row.employeeName}"`,
          row.hours,
          row.rate,
          `"${row.managerName}"`,
          row.submittedAt ? new Date(row.submittedAt).toISOString() : "",
        ].join(","),
      ),
    ]

    const csvString = csvRows.join("\n")
    const blob = new Blob([csvString], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", `payroll-approvals-${new Date().toISOString().split("T")[0]}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span className="flex items-center gap-2">
            <span className="h-2 w-2 rounded-full bg-green-500" /> Submitted
          </span>
          <span className="flex items-center gap-2">
            <span className="h-2 w-2 rounded-full bg-yellow-500" /> Draft
          </span>
          <span className="flex items-center gap-2">
            <span className="h-2 w-2 rounded-full bg-gray-400" /> Missing
          </span>
        </div>
        <Button size="sm" variant="outline" onClick={handleExport}>
          <Download className="mr-2 h-4 w-4" />
          Export CSV
        </Button>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Employee</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Manager</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Submitted At</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((item) => (
              <TableRow key={item.employeeId}>
                <TableCell className="font-medium">{item.employeeName}</TableCell>
                <TableCell>{item.departmentName}</TableCell>
                <TableCell>{item.managerName}</TableCell>
                <TableCell>
                  <StatusBadge status={item.status} />
                </TableCell>
                <TableCell className="text-right text-muted-foreground">
                  {item.submittedAt ? new Date(item.submittedAt).toLocaleString() : "N/A"}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </>
  )
}
