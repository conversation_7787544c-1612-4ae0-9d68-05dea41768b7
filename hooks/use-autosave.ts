"use client"

import { useState, useEffect, useRef, useCallback } from "react"

type AutosaveStatus = "idle" | "saving" | "success" | "error"

export function useAutosave<T>({
  data,
  onSave,
  interval = 2000,
}: {
  data: T
  onSave: (data: T) => Promise<any>
  interval?: number
}) {
  const [status, setStatus] = useState<AutosaveStatus>("idle")
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const initialRenderRef = useRef(true)

  const saveData = useCallback(async () => {
    setStatus("saving")
    try {
      await onSave(data)
      setStatus("success")
    } catch (error) {
      console.error("Autosave failed:", error)
      setStatus("error")
    }
  }, [data, onSave])

  useEffect(() => {
    if (initialRenderRef.current) {
      initialRenderRef.current = false
      return
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    setStatus("idle") // Reset status on new change
    timeoutRef.current = setTimeout(() => {
      saveData()
    }, interval)

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [data, interval, saveData])

  return status
}
