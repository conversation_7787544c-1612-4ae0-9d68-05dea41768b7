import { z } from 'zod'

// Environment validation schema
const envSchema = z.object({
  // Database
  DATABASE_URL: z.string().url('Invalid database URL'),
  
  // Authentication
  CLERK_SECRET_KEY: z.string().min(1, 'Clerk secret key is required'),
  CLERK_PUBLISHABLE_KEY: z.string().min(1, 'Clerk publishable key is required'),
  NEXTAUTH_SECRET: z.string().min(32, 'NextAuth secret must be at least 32 characters'),
  
  // Application
  NODE_ENV: z.enum(['development', 'staging', 'production']).default('development'),
  NEXT_PUBLIC_APP_URL: z.string().url('Invalid app URL').default('http://localhost:3000'),
  
  // Rate Limiting
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).pipe(z.number().positive()).default('100'),
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).pipe(z.number().positive()).default('60000'),
  
  // Logging
  LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
  ENABLE_CONSOLE_LOGGING: z.string().transform(val => val === 'true').default('true'),
  
  // Security
  CSRF_SECRET: z.string().min(32, 'CSRF secret must be at least 32 characters'),
  SESSION_SECRET: z.string().min(32, 'Session secret must be at least 32 characters'),
  
  // Optional external services
  SENTRY_DSN: z.string().url().optional(),
  ANALYTICS_ID: z.string().optional(),
})

// Parse and validate environment variables
function validateEnv() {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join('\n')
      throw new Error(`Environment validation failed:\n${missingVars}`)
    }
    throw error
  }
}

// Export validated environment configuration
export const env = validateEnv()

// Type-safe environment configuration
export type EnvConfig = z.infer<typeof envSchema>

// Helper functions
export const isDevelopment = env.NODE_ENV === 'development'
export const isProduction = env.NODE_ENV === 'production'
export const isStaging = env.NODE_ENV === 'staging'

// Database configuration
export const dbConfig = {
  url: env.DATABASE_URL,
  ssl: isProduction ? { rejectUnauthorized: false } : false,
  max: isProduction ? 20 : 5, // Connection pool size
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
}

// Authentication configuration
export const authConfig = {
  clerkSecretKey: env.CLERK_SECRET_KEY,
  clerkPublishableKey: env.CLERK_PUBLISHABLE_KEY,
  nextAuthSecret: env.NEXTAUTH_SECRET,
  sessionSecret: env.SESSION_SECRET,
}

// Rate limiting configuration
export const rateLimitConfig = {
  maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
  windowMs: env.RATE_LIMIT_WINDOW_MS,
}

// Logging configuration
export const loggingConfig = {
  level: env.LOG_LEVEL,
  enableConsole: env.ENABLE_CONSOLE_LOGGING,
  sentryDsn: env.SENTRY_DSN,
}

// Security configuration
export const securityConfig = {
  csrfSecret: env.CSRF_SECRET,
  sessionSecret: env.SESSION_SECRET,
}

console.log(`🚀 Application starting in ${env.NODE_ENV} mode`)
console.log(`📊 Logging level: ${env.LOG_LEVEL}`)
console.log(`🔒 Security headers enabled: ${isProduction}`)
