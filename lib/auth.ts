import { auth, currentUser } from '@clerk/nextjs'
import { headers } from 'next/headers'
import { redirect } from 'next/navigation'
import type { UserRole } from './schemas'

// Get current user with role information
export async function getCurrentUser() {
  const { userId } = auth()
  
  if (!userId) {
    return null
  }
  
  const user = await currentUser()
  
  if (!user) {
    return null
  }
  
  // Extract role from user metadata (set during user creation/update)
  const role = (user.publicMetadata?.role as UserRole) || 'manager'
  
  return {
    id: userId,
    email: user.emailAddresses[0]?.emailAddress,
    fullName: `${user.firstName} ${user.lastName}`.trim(),
    role,
    imageUrl: user.imageUrl,
  }
}

// Get user role from headers (set by middleware)
export function getUserRoleFromHeaders(): UserRole {
  const headersList = headers()
  const role = headersList.get('x-user-role') as UserRole
  return role || 'manager'
}

// Get user ID from headers (set by middleware)
export function getUserIdFromHeaders(): string | null {
  const headersList = headers()
  return headersList.get('x-user-id')
}

// Check if user has specific permission
export function hasPermission(userRole: UserRole, permission: string): boolean {
  const rolePermissions = {
    'super-admin': ['*'],
    'hr-admin': [
      'employee:read', 'employee:write', 'employee:delete',
      'department:read', 'department:write', 'department:delete',
      'period:read', 'period:write', 'period:delete',
      'appraisal:read', 'appraisal:write',
      'approval:read'
    ],
    'manager': [
      'employee:read',
      'appraisal:read', 'appraisal:write'
    ],
    'accountant': [
      'employee:read',
      'appraisal:read',
      'approval:read', 'approval:export'
    ]
  }
  
  const permissions = rolePermissions[userRole] || []
  return permissions.includes('*') || permissions.includes(permission)
}

// Require authentication (redirect if not authenticated)
export async function requireAuth() {
  const user = await getCurrentUser()
  
  if (!user) {
    redirect('/sign-in')
  }
  
  return user
}

// Require specific role (redirect if insufficient permissions)
export async function requireRole(requiredRole: UserRole | UserRole[]) {
  const user = await requireAuth()
  
  const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
  
  if (!allowedRoles.includes(user.role)) {
    redirect('/dashboard?error=insufficient_permissions')
  }
  
  return user
}

// Require specific permission (redirect if insufficient permissions)
export async function requirePermission(permission: string) {
  const user = await requireAuth()
  
  if (!hasPermission(user.role, permission)) {
    redirect('/dashboard?error=insufficient_permissions')
  }
  
  return user
}

// Check if current user can access employee data
export async function canAccessEmployee(employeeId: string): Promise<boolean> {
  const user = await getCurrentUser()
  
  if (!user) return false
  
  // Super admin and HR admin can access all employees
  if (user.role === 'super-admin' || user.role === 'hr-admin') {
    return true
  }
  
  // Managers can only access their assigned employees
  if (user.role === 'manager') {
    // TODO: Check if this employee is assigned to this manager
    // This would require a database query to check employee_assignments table
    return true // For now, allow all managers to access all employees
  }
  
  // Accountants can read employee data for approvals
  if (user.role === 'accountant') {
    return true
  }
  
  return false
}

// Validate user session and return user info
export async function validateSession() {
  try {
    const { userId } = auth()
    
    if (!userId) {
      throw new Error('No user session found')
    }
    
    const user = await currentUser()
    
    if (!user) {
      throw new Error('User not found')
    }
    
    return {
      userId,
      email: user.emailAddresses[0]?.emailAddress,
      fullName: `${user.firstName} ${user.lastName}`.trim(),
      role: (user.publicMetadata?.role as UserRole) || 'manager',
      isActive: true,
    }
  } catch (error) {
    console.error('Session validation failed:', error)
    throw new Error('Invalid session')
  }
}

// Rate limiting helper
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(
  userId: string, 
  action: string, 
  maxRequests: number = 10, 
  windowMs: number = 60000
): boolean {
  const key = `${userId}:${action}`
  const now = Date.now()
  const userLimit = rateLimitMap.get(key)
  
  if (!userLimit || now > userLimit.resetTime) {
    // Reset or initialize rate limit
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (userLimit.count >= maxRequests) {
    return false // Rate limit exceeded
  }
  
  userLimit.count++
  return true
}

// Audit logging helper
export async function logUserAction(
  action: string, 
  details?: Record<string, any>
) {
  const user = await getCurrentUser()
  
  if (!user) return
  
  const logEntry = {
    userId: user.id,
    userRole: user.role,
    action,
    details,
    timestamp: new Date().toISOString(),
    ip: headers().get('x-forwarded-for') || 'unknown',
    userAgent: headers().get('user-agent') || 'unknown',
  }
  
  // In production, send this to your logging service
  console.log('User Action:', logEntry)
  
  // TODO: Store in database or send to external logging service
}

export type AuthUser = Awaited<ReturnType<typeof getCurrentUser>>
export type SessionUser = Awaited<ReturnType<typeof validateSession>>
