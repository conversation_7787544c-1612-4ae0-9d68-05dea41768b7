"use client"

export type Department = {
  id: string
  name: string
}

export type Manager = {
  id: string // This would be the Clerk user_id
  fullName: string
}

export type Employee = {
  id: string
  fullName: string
  compensation: "hourly" | "monthly"
  rate: number
  departmentId: string
  departmentName?: string
  managerId?: string | null
  managerName?: string | null
  active: boolean
}

export type AppraisalPeriod = {
  id: string
  periodStart: string // ISO date string
  periodEnd: string // ISO date string
  closed: boolean
}

export type AppraisalStatus = "submitted" | "draft" | "not-started"

export type EmployeeAppraisal = {
  employeeId: string
  fullName: string
  departmentName: string
  status: AppraisalStatus
  submittedAt?: string
}

export type UserRole = "super-admin" | "hr-admin" | "manager" | "accountant"

export type MockUser = {
  fullName: string
  role: UserRole
}

export type UserSession = {
  id: string
  full_name: string
  role: UserRole
}

export type AppraisalDetails = {
  id: string
  periodId: string
  employeeId: string
  managerId: string
  q1: "below-expectations" | "meets-expectations" | "exceeds-expectations" | null
  q2: boolean
  q3: string
  q4: string
  q5: string
  status: "draft" | "submitted"
}

export type EmployeeDetails = {
  id: string
  fullName: string
  departmentName: string
  compensation: "hourly" | "monthly"
  rate: number
}

export type AccountingViewData = {
  employeeId: string
  employeeName: string
  departmentName: string
  managerName: string
  status: AppraisalStatus
  submittedAt: string | null
  compensation: "hourly" | "monthly"
  rate: number
  hours: number
}
