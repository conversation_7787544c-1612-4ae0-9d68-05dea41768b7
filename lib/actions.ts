"use server"

import { revalidatePath } from "next/cache"
import { auth } from "@clerk/nextjs"
import { saveDepartment, saveEmployee, savePeriod } from "./data"
import {
  validateDepartment,
  validateEmployee,
  validateAppraisal,
  validateAppraisalSubmission,
  validatePeriod,
  departmentFormSchema,
  employeeFormSchema,
  appraisalPeriodFormSchema,
  appraisalFormSchema
} from "./schemas"
import {
  requirePermission,
  checkRateLimit,
  logUserAction,
  validateSession
} from "./auth"
import type { AppraisalDetails, Department, Employee, AppraisalPeriod } from "./types"

// Error classes for better error handling
class AuthenticationError extends Error {
  constructor(message = "Authentication required") {
    super(message)
    this.name = "AuthenticationError"
  }
}

class AuthorizationError extends Error {
  constructor(message = "Insufficient permissions") {
    super(message)
    this.name = "AuthorizationError"
  }
}

class ValidationError extends Error {
  constructor(message = "Validation failed") {
    super(message)
    this.name = "ValidationError"
  }
}

class RateLimitError extends Error {
  constructor(message = "Rate limit exceeded") {
    super(message)
    this.name = "RateLimitError"
  }
}

// Helper function to handle server action errors
function handleServerActionError(error: unknown) {
  console.error("Server action error:", error)

  if (error instanceof AuthenticationError) {
    return { success: false, error: "Authentication required", code: "AUTH_REQUIRED" }
  }

  if (error instanceof AuthorizationError) {
    return { success: false, error: "Insufficient permissions", code: "INSUFFICIENT_PERMISSIONS" }
  }

  if (error instanceof ValidationError) {
    return { success: false, error: error.message, code: "VALIDATION_ERROR" }
  }

  if (error instanceof RateLimitError) {
    return { success: false, error: "Too many requests. Please try again later.", code: "RATE_LIMIT" }
  }

  // Generic error for unexpected issues
  return {
    success: false,
    error: "An unexpected error occurred. Please try again.",
    code: "INTERNAL_ERROR"
  }
}

export async function saveDepartmentAction(formData: FormData) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'department-save', 10, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('department:write')

    // Extract and validate data
    const rawData = {
      id: formData.get('id') as string || undefined,
      name: formData.get('name') as string,
    }

    const validatedData = departmentFormSchema.parse(rawData)

    // Log the action
    await logUserAction('department:save', { departmentName: validatedData.name })

    // Save to database
    await saveDepartment(validatedData)

    // Revalidate cache
    revalidatePath("/dashboard/departments")

    return { success: true, message: "Department saved successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function saveEmployeeAction(formData: FormData) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'employee-save', 10, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('employee:write')

    // Extract and validate data
    const rawData = {
      id: formData.get('id') as string || undefined,
      fullName: formData.get('fullName') as string,
      compensation: formData.get('compensation') as 'hourly' | 'monthly',
      rate: Number(formData.get('rate')),
      departmentId: formData.get('departmentId') as string,
      managerId: formData.get('managerId') as string || null,
      active: formData.get('active') === 'true',
    }

    const validatedData = employeeFormSchema.parse(rawData)

    // Log the action
    await logUserAction('employee:save', {
      employeeName: validatedData.fullName,
      isUpdate: !!rawData.id
    })

    // Save to database
    await saveEmployee(validatedData)

    // Revalidate cache
    revalidatePath("/dashboard/employees")

    return { success: true, message: "Employee saved successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function savePeriodAction(formData: FormData) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'period-save', 5, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('period:write')

    // Extract and validate data
    const rawData = {
      id: formData.get('id') as string || undefined,
      periodStart: formData.get('periodStart') as string,
      periodEnd: formData.get('periodEnd') as string,
      closed: formData.get('closed') === 'true',
    }

    const validatedData = appraisalPeriodFormSchema.parse(rawData)

    // Log the action
    await logUserAction('period:save', {
      periodStart: validatedData.periodStart,
      periodEnd: validatedData.periodEnd,
      isUpdate: !!rawData.id
    })

    // Save to database
    await savePeriod(validatedData)

    // Revalidate cache
    revalidatePath("/dashboard/periods")

    return { success: true, message: "Appraisal Period saved successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function saveAppraisalDraftAction(draft: Partial<AppraisalDetails>) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting (more generous for autosave)
    if (!checkRateLimit(session.userId, 'appraisal-draft', 30, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('appraisal:write')

    // Validate draft data (less strict than submission)
    const validatedData = appraisalFormSchema.partial().parse(draft)

    // Log the action (less verbose for drafts)
    console.log(`Saving draft for user ${session.userId}`, {
      employeeId: draft.employeeId,
      hasQ1: !!draft.q1,
      hasQ2: draft.q2 !== undefined,
      hasQ3: !!draft.q3,
      hasQ4: !!draft.q4,
      hasQ5: !!draft.q5,
    })

    // Simulate network delay (in real app, save to database)
    await new Promise((res) => setTimeout(res, 750))

    // TODO: In real implementation, save to appraisals table
    // await saveAppraisalDraft(validatedData)

    console.log("Draft saved successfully.")
    return { success: true, message: "Draft saved successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function submitAppraisalAction(appraisal: Partial<AppraisalDetails>) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting (stricter for submissions)
    if (!checkRateLimit(session.userId, 'appraisal-submit', 5, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('appraisal:write')

    // Validate submission data (strict validation)
    const submissionData = {
      ...appraisal,
      status: 'submitted' as const,
    }

    const validatedData = validateAppraisalSubmission(submissionData)

    // Additional business logic validation
    if (!validatedData.employeeId) {
      throw new ValidationError("Employee ID is required")
    }

    // Log the action
    await logUserAction('appraisal:submit', {
      employeeId: validatedData.employeeId,
      managerId: session.userId,
      submittedAt: new Date().toISOString()
    })

    console.log(`Submitting appraisal for employee ${validatedData.employeeId}`)

    // Simulate network delay (in real app, update database)
    await new Promise((res) => setTimeout(res, 1000))

    // TODO: In real implementation, update appraisal status in database
    // await updateAppraisalStatus(validatedData.id, 'submitted')

    // Revalidate relevant pages
    revalidatePath("/dashboard")
    revalidatePath(`/dashboard/appraisal/${validatedData.employeeId}`)
    revalidatePath("/dashboard/approvals")

    console.log("Appraisal submitted successfully.")
    return { success: true, message: "Appraisal submitted successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

// Additional secure action for deleting entities
export async function deleteEmployeeAction(employeeId: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'employee-delete', 3, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check (only super-admin and hr-admin can delete)
    await requirePermission('employee:delete')

    // Validate employee ID
    if (!employeeId || typeof employeeId !== 'string') {
      throw new ValidationError("Valid employee ID is required")
    }

    // Log the action
    await logUserAction('employee:delete', { employeeId })

    // TODO: In real implementation, soft delete employee
    // await softDeleteEmployee(employeeId)

    // Revalidate cache
    revalidatePath("/dashboard/employees")

    return { success: true, message: "Employee deleted successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

// Bulk action for exporting data
export async function exportAppraisalsAction(filters?: {
  departmentId?: string
  periodId?: string
  status?: 'submitted' | 'draft'
}) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting (stricter for exports)
    if (!checkRateLimit(session.userId, 'export-data', 2, 300000)) { // 2 per 5 minutes
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('approval:export')

    // Log the action
    await logUserAction('data:export', {
      filters,
      exportType: 'appraisals'
    })

    // TODO: In real implementation, generate and return export data
    // const exportData = await generateAppraisalExport(filters)

    return {
      success: true,
      message: "Export generated successfully.",
      // data: exportData
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}
