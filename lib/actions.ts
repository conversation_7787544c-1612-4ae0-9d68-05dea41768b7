"use server"

import { revalidatePath } from "next/cache"
import { saveDepartment, saveEmployee, savePeriod } from "./data"
import type { AppraisalDetails, Department, Employee, AppraisalPeriod } from "./types"

export async function saveDepartmentAction(department: Partial<Department>) {
  await saveDepartment(department)
  revalidatePath("/dashboard/departments")
  return { success: true, message: "Department saved successfully." }
}

export async function saveEmployeeAction(employee: Partial<Employee>) {
  await saveEmployee(employee)
  revalidatePath("/dashboard/employees")
  return { success: true, message: "Employee saved successfully." }
}

export async function savePeriodAction(period: Partial<AppraisalPeriod>) {
  await savePeriod(period)
  revalidatePath("/dashboard/periods")
  return { success: true, message: "Appraisal Period saved successfully." }
}

export async function saveAppraisalDraftAction(draft: Partial<AppraisalDetails>) {
  console.log("Saving draft...", draft)
  // Simulate a network delay
  await new Promise((res) => setTimeout(res, 750))
  // In a real app, this would save to the 'appraisals' table.
  console.log("Draft saved.")
  return { success: true, message: "Draft saved successfully." }
}

export async function submitAppraisalAction(appraisal: Partial<AppraisalDetails>) {
  console.log("Submitting appraisal...", appraisal)
  await new Promise((res) => setTimeout(res, 1000))
  // In a real app, this would update the appraisal status to 'submitted'.
  console.log("Appraisal submitted.")
  revalidatePath("/dashboard")
  revalidatePath(`/dashboard/appraisal/${appraisal.employeeId}`)
  return { success: true, message: "Appraisal submitted successfully." }
}
