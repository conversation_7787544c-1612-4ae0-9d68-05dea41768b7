import { Pool, PoolClient, QueryResult } from 'pg'
import { env, dbConfig } from './config'

// Global connection pool
let pool: Pool | null = null

// Initialize database connection pool
function getPool(): Pool {
  if (!pool) {
    pool = new Pool({
      connectionString: env.DATABASE_URL,
      ssl: dbConfig.ssl,
      max: dbConfig.max,
      idleTimeoutMillis: dbConfig.idleTimeoutMillis,
      connectionTimeoutMillis: dbConfig.connectionTimeoutMillis,
    })

    // Handle pool errors
    pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err)
      process.exit(-1)
    })

    console.log(`📊 Database pool initialized with max ${dbConfig.max} connections`)
  }

  return pool
}

// Execute a query with automatic connection management
export async function query<T = any>(
  text: string, 
  params?: any[]
): Promise<QueryResult<T>> {
  const start = Date.now()
  const pool = getPool()
  
  try {
    const result = await pool.query<T>(text, params)
    const duration = Date.now() - start
    
    if (env.NODE_ENV === 'development') {
      console.log('Executed query', { 
        text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        duration: `${duration}ms`,
        rows: result.rowCount 
      })
    }
    
    return result
  } catch (error) {
    console.error('Database query error:', {
      query: text,
      params,
      error: error instanceof Error ? error.message : error
    })
    throw error
  }
}

// Execute a transaction
export async function transaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const pool = getPool()
  const client = await pool.connect()
  
  try {
    await client.query('BEGIN')
    const result = await callback(client)
    await client.query('COMMIT')
    return result
  } catch (error) {
    await client.query('ROLLBACK')
    throw error
  } finally {
    client.release()
  }
}

// Health check function
export async function healthCheck(): Promise<boolean> {
  try {
    const result = await query('SELECT 1 as health')
    return result.rows[0]?.health === 1
  } catch (error) {
    console.error('Database health check failed:', error)
    return false
  }
}

// Close all connections (for graceful shutdown)
export async function closePool(): Promise<void> {
  if (pool) {
    await pool.end()
    pool = null
    console.log('📊 Database pool closed')
  }
}

// Database-specific query builders
export const db = {
  // Departments
  async getDepartments() {
    const result = await query(`
      SELECT id, name, created_at
      FROM departments 
      ORDER BY name
    `)
    return result.rows
  },

  async createDepartment(name: string) {
    const result = await query(`
      INSERT INTO departments (name) 
      VALUES ($1) 
      RETURNING id, name, created_at
    `, [name])
    return result.rows[0]
  },

  async updateDepartment(id: string, name: string) {
    const result = await query(`
      UPDATE departments 
      SET name = $1 
      WHERE id = $2 
      RETURNING id, name, created_at
    `, [name, id])
    return result.rows[0]
  },

  async deleteDepartment(id: string) {
    await query('DELETE FROM departments WHERE id = $1', [id])
  },

  // Employees
  async getEmployees() {
    const result = await query(`
      SELECT 
        e.id,
        e.full_name,
        e.compensation,
        e.rate,
        e.active,
        e.created_at,
        d.id as department_id,
        d.name as department_name,
        m.user_id as manager_id,
        m.full_name as manager_name
      FROM employees e
      LEFT JOIN departments d ON e.department_id = d.id
      LEFT JOIN managers m ON e.manager_id = m.user_id
      WHERE e.active = true
      ORDER BY e.full_name
    `)
    return result.rows
  },

  async getEmployeeById(id: string) {
    const result = await query(`
      SELECT 
        e.id,
        e.full_name,
        e.compensation,
        e.rate,
        e.active,
        e.created_at,
        d.id as department_id,
        d.name as department_name,
        m.user_id as manager_id,
        m.full_name as manager_name
      FROM employees e
      LEFT JOIN departments d ON e.department_id = d.id
      LEFT JOIN managers m ON e.manager_id = m.user_id
      WHERE e.id = $1
    `, [id])
    return result.rows[0]
  },

  async createEmployee(data: {
    fullName: string
    compensation: 'hourly' | 'monthly'
    rate: number
    departmentId: string
    managerId?: string | null
  }) {
    const result = await query(`
      INSERT INTO employees (full_name, compensation, rate, department_id, manager_id)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id, full_name, compensation, rate, department_id, manager_id, active, created_at
    `, [data.fullName, data.compensation, data.rate, data.departmentId, data.managerId])
    return result.rows[0]
  },

  async updateEmployee(id: string, data: {
    fullName?: string
    compensation?: 'hourly' | 'monthly'
    rate?: number
    departmentId?: string
    managerId?: string | null
    active?: boolean
  }) {
    const fields = []
    const values = []
    let paramCount = 1

    if (data.fullName !== undefined) {
      fields.push(`full_name = $${paramCount++}`)
      values.push(data.fullName)
    }
    if (data.compensation !== undefined) {
      fields.push(`compensation = $${paramCount++}`)
      values.push(data.compensation)
    }
    if (data.rate !== undefined) {
      fields.push(`rate = $${paramCount++}`)
      values.push(data.rate)
    }
    if (data.departmentId !== undefined) {
      fields.push(`department_id = $${paramCount++}`)
      values.push(data.departmentId)
    }
    if (data.managerId !== undefined) {
      fields.push(`manager_id = $${paramCount++}`)
      values.push(data.managerId)
    }
    if (data.active !== undefined) {
      fields.push(`active = $${paramCount++}`)
      values.push(data.active)
    }

    if (fields.length === 0) {
      throw new Error('No fields to update')
    }

    values.push(id)
    const result = await query(`
      UPDATE employees 
      SET ${fields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING id, full_name, compensation, rate, department_id, manager_id, active, created_at
    `, values)
    
    return result.rows[0]
  },

  async softDeleteEmployee(id: string) {
    await query('UPDATE employees SET active = false WHERE id = $1', [id])
  },

  // Managers
  async getManagers() {
    const result = await query(`
      SELECT user_id as id, full_name
      FROM managers
      ORDER BY full_name
    `)
    return result.rows
  },

  async createManager(userId: string, fullName: string) {
    const result = await query(`
      INSERT INTO managers (user_id, full_name)
      VALUES ($1, $2)
      RETURNING user_id as id, full_name, created_at
    `, [userId, fullName])
    return result.rows[0]
  },

  // Appraisal Periods
  async getAppraisalPeriods() {
    const result = await query(`
      SELECT id, period_start, period_end, closed, created_at
      FROM appraisal_periods
      ORDER BY period_start DESC
    `)
    return result.rows
  },

  async createAppraisalPeriod(data: {
    periodStart: string
    periodEnd: string
    closed?: boolean
  }) {
    const result = await query(`
      INSERT INTO appraisal_periods (period_start, period_end, closed)
      VALUES ($1, $2, $3)
      RETURNING id, period_start, period_end, closed, created_at
    `, [data.periodStart, data.periodEnd, data.closed || false])
    return result.rows[0]
  },

  async updateAppraisalPeriod(id: string, data: {
    periodStart?: string
    periodEnd?: string
    closed?: boolean
  }) {
    const fields = []
    const values = []
    let paramCount = 1

    if (data.periodStart !== undefined) {
      fields.push(`period_start = $${paramCount++}`)
      values.push(data.periodStart)
    }
    if (data.periodEnd !== undefined) {
      fields.push(`period_end = $${paramCount++}`)
      values.push(data.periodEnd)
    }
    if (data.closed !== undefined) {
      fields.push(`closed = $${paramCount++}`)
      values.push(data.closed)
    }

    if (fields.length === 0) {
      throw new Error('No fields to update')
    }

    values.push(id)
    const result = await query(`
      UPDATE appraisal_periods 
      SET ${fields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING id, period_start, period_end, closed, created_at
    `, values)
    
    return result.rows[0]
  },

  // Appraisals
  async getAppraisalByEmployeeId(employeeId: string, periodId?: string) {
    let queryText = `
      SELECT id, period_id, employee_id, manager_id, q1, q2, q3, q4, q5, status, created_at, submitted_at
      FROM appraisals
      WHERE employee_id = $1
    `
    const params = [employeeId]

    if (periodId) {
      queryText += ' AND period_id = $2'
      params.push(periodId)
    }

    queryText += ' ORDER BY created_at DESC LIMIT 1'

    const result = await query(queryText, params)
    return result.rows[0]
  },

  async saveAppraisalDraft(data: {
    periodId: string
    employeeId: string
    managerId: string
    q1?: string | null
    q2?: boolean
    q3?: string
    q4?: string
    q5?: string
  }) {
    const result = await query(`
      INSERT INTO appraisals (period_id, employee_id, manager_id, q1, q2, q3, q4, q5, status)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 'draft')
      ON CONFLICT (period_id, employee_id)
      DO UPDATE SET
        q1 = EXCLUDED.q1,
        q2 = EXCLUDED.q2,
        q3 = EXCLUDED.q3,
        q4 = EXCLUDED.q4,
        q5 = EXCLUDED.q5
      RETURNING id, period_id, employee_id, manager_id, q1, q2, q3, q4, q5, status, created_at
    `, [data.periodId, data.employeeId, data.managerId, data.q1, data.q2, data.q3, data.q4, data.q5])
    return result.rows[0]
  },

  async submitAppraisal(id: string) {
    const result = await query(`
      UPDATE appraisals 
      SET status = 'submitted', submitted_at = NOW()
      WHERE id = $1
      RETURNING id, period_id, employee_id, manager_id, q1, q2, q3, q4, q5, status, created_at, submitted_at
    `, [id])
    return result.rows[0]
  }
}

console.log('📊 Database module loaded successfully')
