import type {
  EmployeeAppraisal,
  MockUser,
  EmployeeDetails,
  AppraisalDetails,
  AccountingViewData,
  UserRole,
  Employee,
  Department,
  Manager,
  AppraisalPeriod,
} from "./types"
import { randomUUID } from "crypto"

// --- MOCK DATA STORE ---
let mockDepartments: Department[] = [
  { id: "dept-1", name: "Engineering" },
  { id: "dept-2", name: "Product" },
  { id: "dept-3", name: "Design" },
  { id: "dept-4", name: "Marketing" },
]

const mockManagers: Manager[] = [
  { id: "user_2i5Yl5YqYqYqYqYqYqYqYqYqYqY", fullName: "Francesco" },
  { id: "user_3j6Zl6ZqZqZqZqZqZqZqZqZqZqZ", fullName: "<PERSON>az<PERSON>" },
]

let mockEmployees: Employee[] = [
  {
    id: "1",
    fullName: "<PERSON><PERSON>",
    departmentId: "dept-1",
    managerId: "user_2i5Yl5YqYqYqYqYqYqYqYqYqYqY",
    compensation: "monthly",
    rate: 10000.0,
    active: true,
  },
  {
    id: "2",
    fullName: "Alice",
    departmentId: "dept-1",
    managerId: "user_2i5Yl5YqYqYqYqYqYqYqYqYqYqY",
    compensation: "monthly",
    rate: 9500.0,
    active: true,
  },
  {
    id: "3",
    fullName: "Charlie",
    departmentId: "dept-1",
    managerId: "user_2i5Yl5YqYqYqYqYqYqYqYqYqYqY",
    compensation: "hourly",
    rate: 75.0,
    active: true,
  },
  {
    id: "4",
    fullName: "David",
    departmentId: "dept-2",
    managerId: "user_2i5Yl5YqYqYqYqYqYqYqYqYqYqY",
    compensation: "monthly",
    rate: 11000.0,
    active: true,
  },
  {
    id: "5",
    fullName: "Eve",
    departmentId: "dept-2",
    managerId: "user_2i5Yl5YqYqYqYqYqYqYqYqYqYqY",
    compensation: "hourly",
    rate: 80.0,
    active: false,
  },
]

let mockPeriods: AppraisalPeriod[] = [
  {
    id: "period-1",
    periodStart: "2025-07-01",
    periodEnd: "2025-07-31",
    closed: false,
  },
  {
    id: "period-2",
    periodStart: "2025-06-01",
    periodEnd: "2025-06-30",
    closed: true,
  },
]

// --- DATA ACCESS FUNCTIONS ---

export async function getLoggedInUser(role: UserRole = "manager"): Promise<MockUser> {
  const users: Record<UserRole, MockUser> = {
    manager: { fullName: "Francesco", role: "manager" },
    accountant: { fullName: "Grace", role: "accountant" },
    "hr-admin": { fullName: "Joelle", role: "hr-admin" },
    "super-admin": { fullName: "Bob Wazneh", role: "super-admin" },
  }
  return users[role]
}

// Departments
export async function getDepartments(): Promise<Department[]> {
  return mockDepartments
}
export async function saveDepartment(dept: Partial<Department>): Promise<void> {
  if (dept.id) {
    mockDepartments = mockDepartments.map((d) => (d.id === dept.id ? { ...d, ...dept } : d))
  } else {
    mockDepartments.push({ id: `dept-${randomUUID()}`, name: dept.name! })
  }
}

// Managers
export async function getManagers(): Promise<Manager[]> {
  return mockManagers
}

// Employees
export async function getEmployees(): Promise<Employee[]> {
  const departments = await getDepartments()
  const managers = await getManagers()
  return mockEmployees.map((emp) => ({
    ...emp,
    departmentName: departments.find((d) => d.id === emp.departmentId)?.name ?? "N/A",
    managerName: managers.find((m) => m.id === emp.managerId)?.fullName ?? "N/A",
  }))
}
export async function saveEmployee(emp: Partial<Employee>): Promise<void> {
  if (emp.id) {
    mockEmployees = mockEmployees.map((e) => (e.id === emp.id ? { ...e, ...emp } : e))
  } else {
    mockEmployees.push({ id: randomUUID(), ...emp } as Employee)
  }
}

// Periods
export async function getPeriods(): Promise<AppraisalPeriod[]> {
  return mockPeriods
}
export async function savePeriod(period: Partial<AppraisalPeriod>): Promise<void> {
  if (period.id) {
    mockPeriods = mockPeriods.map((p) => (p.id === period.id ? { ...p, ...period } : p))
  } else {
    mockPeriods.unshift({ id: `period-${randomUUID()}`, ...period } as AppraisalPeriod)
  }
}

// --- PREVIOUSLY CREATED MOCK FUNCTIONS ---
// (These are simplified to derive from the main mock data)

export async function getManagerAppraisals(): Promise<EmployeeAppraisal[]> {
  const employees = await getEmployees()
  return employees.slice(0, 5).map((emp) => ({
    employeeId: emp.id,
    fullName: emp.fullName,
    departmentName: emp.departmentName!,
    status: emp.id === "1" ? "submitted" : emp.id === "2" ? "draft" : "not-started",
    submittedAt: emp.id === "1" ? "2025-07-05T10:00:00Z" : undefined,
  }))
}

export async function getEmployeeDetails(employeeId: string): Promise<EmployeeDetails | null> {
  const employee = (await getEmployees()).find((e) => e.id === employeeId)
  if (!employee) return null
  return {
    id: employee.id,
    fullName: employee.fullName,
    departmentName: employee.departmentName!,
    compensation: employee.compensation,
    rate: employee.rate,
  }
}

export async function getAppraisalDetails(employeeId: string): Promise<AppraisalDetails | null> {
  const mockAppraisalDetails: Record<string, AppraisalDetails> = {
    "1": {
      id: "appraisal-1",
      periodId: "period-1",
      employeeId: "1",
      managerId: "user_2i5Yl5YqYqYqYqYqYqYqYqYqYqY",
      q1: "exceeds-expectations",
      q2: true,
      q3: "Project Phoenix",
      q4: "Great teamwork and leadership on the new feature.",
      q5: "Ready for senior role.",
      status: "submitted",
    },
    "2": {
      id: "appraisal-2",
      periodId: "period-1",
      employeeId: "2",
      managerId: "user_2i5Yl5YqYqYqYqYqYqYqYqYqYqY",
      q1: "meets-expectations",
      q2: false,
      q3: "Component Library",
      q4: "",
      q5: "",
      status: "draft",
    },
  }
  return mockAppraisalDetails[employeeId] || null
}

export async function getAccountingData(): Promise<AccountingViewData[]> {
  const employees = await getEmployees()
  return employees.map((emp) => ({
    employeeId: emp.id,
    employeeName: emp.fullName,
    departmentName: emp.departmentName!,
    managerName: emp.managerName!,
    status: emp.id === "1" || emp.id === "5" ? "submitted" : emp.id === "2" ? "draft" : "not-started",
    submittedAt: emp.id === "1" ? "2025-07-05T10:00:00Z" : emp.id === "5" ? "2025-07-04T14:30:00Z" : null,
    compensation: emp.compensation,
    rate: emp.rate,
    hours: emp.compensation === "hourly" ? 155 : 160,
  }))
}
