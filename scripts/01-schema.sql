-- prettier-ignore
-- <PERSON>reate custom types
CREATE TYPE appraisal_status AS ENUM ('draft', 'submitted');
CREATE TYPE user_role AS ENUM ('super-admin', 'hr-admin', 'manager', 'accountant');

-- Create tables
CREATE TABLE departments (
  id              uuid primary key default gen_random_uuid(),
  name            text not null unique,
  created_at      timestamptz default now()
);

CREATE TABLE employees (
  id              uuid primary key default gen_random_uuid(),
  full_name       text not null,
  compensation    text not null check (compensation in ('hourly','monthly')),
  rate            numeric(10,2) not null,
  department_id   uuid references departments(id),
  active          boolean default true,
  created_at      timestamptz default now()
);

CREATE TABLE managers (
  user_id         uuid primary key,                   -- This would be the Clerk user ID
  full_name       text not null,
  created_at      timestamptz default now()
);

CREATE TABLE user_roles (
  user_id         uuid primary key references managers(user_id),
  role            user_role not null
);

CREATE TABLE employee_assignments (
  employee_id     uuid references employees(id),
  manager_id      uuid references managers(user_id),
  primary key (employee_id, manager_id)
);

CREATE TABLE appraisal_periods (
  id              uuid primary key default gen_random_uuid(),
  period_start    date not null,
  period_end      date not null,
  closed          boolean default false,
  created_at      timestamptz default now()
);

CREATE TABLE appraisals (
  id              uuid primary key default gen_random_uuid(),
  period_id       uuid not null references appraisal_periods(id),
  employee_id     uuid not null references employees(id),
  manager_id      uuid not null references managers(user_id),
  q1              text,
  q2              text,
  q3              text,
  q4              text,
  q5              text,
  status          appraisal_status default 'draft',
  created_at      timestamtptz default now(),
  submitted_at    timestamptz,
  unique(period_id, employee_id)
);
